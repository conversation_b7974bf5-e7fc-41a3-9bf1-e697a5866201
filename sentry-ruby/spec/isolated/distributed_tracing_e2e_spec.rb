# frozen_string_literal: true

# isolated tests need a SimpleCov name otherwise they will overwrite coverage
SimpleCov.command_name "RSpecIsolatedDistributedTracingE2E"

require_relative "e2e_test_helper"
require "webmock/rspec"

RSpec.describe "Distributed Tracing E2E with Sample Rand Propagation", webmock: false do
  include Capybara::DSL

  before(:all) do
    WebMock.disable!
    E2ETestHelper.setup_capybara
  end

  after(:all) do
    WebMock.enable!
  end

  before(:each) do
    # Clear events before each test
    clear_rails_events
  end

  it "propagates sample_rand correctly across distributed trace", js: true do
    visit "/"

    expect(page).to have_content("Svelte Mini App", wait: 15)
    expect(page).to have_button("Trigger Error", wait: 10)
    click_button "trigger-error-btn"
    expect(page).to have_content("Error:", wait: 10)

    events_data = get_rails_events

    expect(events_data["event_count"]).to be > 0

    error_events = events_data["events"].select { |event| event["exception"] }
    expect(error_events).not_to be_empty

    error_event = error_events.first
    exception_values = error_event.dig("exception", "values")
    expect(exception_values).not_to be_empty
    expect(exception_values.first["type"]).to eq("ZeroDivisionError")

    transaction_events = events_data["events"].select { |event| event["type"] == "transaction" }

    expect(error_event.dig("contexts", "trace")).not_to be_nil
    error_trace_id = error_event.dig("contexts", "trace", "trace_id")
    expect(error_trace_id).to match(/\A[a-f0-9]{32}\z/)

    if transaction_events.any?
      transaction_event = transaction_events.first
      trace_context = transaction_event.dig("contexts", "trace")

      expect(trace_context).not_to be_nil

      transaction_trace_id = trace_context["trace_id"]

      expect(transaction_trace_id).to match(/\A[a-f0-9]{32}\z/)
      expect(error_trace_id).to eq(transaction_trace_id)

      if transaction_event["_meta"] && transaction_event["_meta"]["dsc"]
        dsc = transaction_event["_meta"]["dsc"]
        expect(dsc).to include("sample_rand")

        sample_rand = dsc["sample_rand"]
        expect(sample_rand).to match(/\A0\.\d{6}\z/)
      end
    end

    # Also check if we can find sample_rand in any envelope DSC
    events_data["envelopes"].each do |envelope|
      envelope["items"].each do |item|
        if item["payload"] && item["payload"]["_meta"] && item["payload"]["_meta"]["dsc"]
          dsc = item["payload"]["_meta"]["dsc"]
          if dsc["sample_rand"]
            puts "Sample rand found in envelope DSC: #{dsc['sample_rand']}"
            expect(dsc["sample_rand"]).to match(/\A0\.\d{6}\z/)
          end
        end
      end
    end

    puts "Successfully verified distributed tracing with sample rand propagation!"
    puts "Events captured: #{events_data['event_count']}"
    puts "Error event trace ID: #{error_trace_id}"
  end

  private

  def get_rails_events
    require "net/http"
    require "json"

    uri = URI("http://localhost:5000/events")
    response = Net::HTTP.get_response(uri)



    if response.body.empty?
      { "events" => [], "envelopes" => [], "event_count" => 0, "envelope_count" => 0 }
    else
      JSON.parse(response.body)
    end
  end

  def clear_rails_events
    require "net/http"

    uri = URI("http://localhost:5000/events")
    http = Net::HTTP.new(uri.host, uri.port)
    request = Net::HTTP::Delete.new(uri)
    http.request(request)
  end
end
